﻿  ui.cc
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(69,21): error C2660: 'strcpy_s': function does not take 2 arguments
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h(32,30):
      see declaration of 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h(124,1):
      could be 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(69,43):
          'initializing': cannot convert from 'std::string' to 'const char *'
              C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(69,43):
              No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(69,21):
      while trying to match the argument list '(char [128], std::string)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(75,25): error C2660: 'strcpy_s': function does not take 2 arguments
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h(32,30):
      see declaration of 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h(124,1):
      could be 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(75,73):
          'initializing': cannot convert from 'std::string' to 'const char *'
              C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(75,73):
              No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(75,25):
      while trying to match the argument list '(char [255], std::string)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(94,25): error C2660: 'strcpy_s': function does not take 2 arguments
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h(32,30):
      see declaration of 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h(124,1):
      could be 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(94,73):
          'initializing': cannot convert from 'std::string' to 'const char *'
              C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(94,73):
              No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(94,25):
      while trying to match the argument list '(char [255], std::string)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(218,38): error C2664: 'ImVec2 ImGui::CalcTextSize(const char *,const char *,bool,float)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(218,51):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(859,29):
      see declaration of 'ImGui::CalcTextSize'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(218,38):
      while trying to match the argument list '(std::string)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(370,39): error C2664: 'ImVec2 ImGui::CalcTextSize(const char *,const char *,bool,float)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(370,52):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(859,29):
      see declaration of 'ImGui::CalcTextSize'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(370,39):
      while trying to match the argument list '(std::string)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(384,20): error C2664: 'bool ImGui::InputTextWithHint(const char *,const char *,char *,size_t,ImGuiInputTextFlags,ImGuiInputTextCallback,void *)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(384,38):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(553,29):
      see declaration of 'ImGui::InputTextWithHint'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(384,20):
      while trying to match the argument list '(std::string, std::string, char [16], size_t, ImGuiInputTextFlags_)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(405,25): error C2660: 'strcpy_s': function does not take 2 arguments
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h(32,30):
      see declaration of 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h(124,1):
      could be 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(405,73):
          'initializing': cannot convert from 'std::string' to 'const char *'
              C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(405,73):
              No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(405,25):
      while trying to match the argument list '(char [255], std::string)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(440,20): error C2664: 'bool ImGui::InputTextWithHint(const char *,const char *,char *,size_t,ImGuiInputTextFlags,ImGuiInputTextCallback,void *)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(440,38):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(553,29):
      see declaration of 'ImGui::InputTextWithHint'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(440,20):
      while trying to match the argument list '(std::string, std::string, char [64], size_t)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(450,20): error C2664: 'bool ImGui::InputTextWithHint(const char *,const char *,char *,size_t,ImGuiInputTextFlags,ImGuiInputTextCallback,void *)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(450,38):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(553,29):
      see declaration of 'ImGui::InputTextWithHint'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(450,20):
      while trying to match the argument list '(std::string, std::string, char [64], size_t, ImGuiInputTextFlags)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(455,24): error C2664: 'bool ImGui::Button(const char *,const ImVec2 &)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(455,31):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(480,29):
      see declaration of 'ImGui::Button'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(455,24):
      while trying to match the argument list '(std::string, ImVec2)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(465,20): error C2664: 'bool ImGui::InputTextWithHint(const char *,const char *,char *,size_t,ImGuiInputTextFlags,ImGuiInputTextCallback,void *)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(465,38):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(553,29):
      see declaration of 'ImGui::InputTextWithHint'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(465,20):
      while trying to match the argument list '(std::string, std::string, char [128], size_t)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(486,25): error C2660: 'strcpy_s': function does not take 2 arguments
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h(32,30):
      see declaration of 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h(124,1):
      could be 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(486,73):
          'initializing': cannot convert from 'std::string' to 'const char *'
              C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(486,73):
              No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(486,25):
      while trying to match the argument list '(char [255], std::string)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(517,20): error C2664: 'bool ImGui::InputTextWithHint(const char *,const char *,char *,size_t,ImGuiInputTextFlags,ImGuiInputTextCallback,void *)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(517,38):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(553,29):
      see declaration of 'ImGui::InputTextWithHint'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(517,20):
      while trying to match the argument list '(std::string, std::string, char [64], size_t)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(527,20): error C2664: 'bool ImGui::InputTextWithHint(const char *,const char *,char *,size_t,ImGuiInputTextFlags,ImGuiInputTextCallback,void *)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(527,38):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(553,29):
      see declaration of 'ImGui::InputTextWithHint'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(527,20):
      while trying to match the argument list '(std::string, std::string, char [64], size_t, ImGuiInputTextFlags)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(533,24): error C2664: 'bool ImGui::Button(const char *,const ImVec2 &)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(533,31):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(480,29):
      see declaration of 'ImGui::Button'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(533,24):
      while trying to match the argument list '(std::string, ImVec2)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(560,21): error C2660: 'strcpy_s': function does not take 2 arguments
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h(32,30):
      see declaration of 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h(124,1):
      could be 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(560,69):
          'initializing': cannot convert from 'std::string' to 'const char *'
              C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(560,69):
              No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(560,21):
      while trying to match the argument list '(char [255], std::string)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(592,40): error C2664: 'ImVec2 ImGui::CalcTextSize(const char *,const char *,bool,float)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(592,53):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(859,29):
      see declaration of 'ImGui::CalcTextSize'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(592,40):
      while trying to match the argument list '(std::string)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(593,40): error C2664: 'ImVec2 ImGui::CalcTextSize(const char *,const char *,bool,float)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(593,53):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(859,29):
      see declaration of 'ImGui::CalcTextSize'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(593,40):
      while trying to match the argument list '(std::string)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(606,24): error C2665: 'ImGui::Selectable': no overloaded function could convert all the argument types
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(600,29):
      could be 'bool ImGui::Selectable(const char *,bool *,ImGuiSelectableFlags,const ImVec2 &)'
          C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(606,24):
          'bool ImGui::Selectable(const char *,bool *,ImGuiSelectableFlags,const ImVec2 &)': cannot convert argument 1 from 'std::string' to 'const char *'
              C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(606,35):
              No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(599,29):
      or       'bool ImGui::Selectable(const char *,bool,ImGuiSelectableFlags,const ImVec2 &)'
          C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(606,24):
          'bool ImGui::Selectable(const char *,bool,ImGuiSelectableFlags,const ImVec2 &)': cannot convert argument 1 from 'std::string' to 'const char *'
              C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(606,35):
              No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(606,24):
      while trying to match the argument list '(std::string, bool, ImGuiSelectableFlags_, ImVec2)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(625,40): error C2664: 'ImVec2 ImGui::CalcTextSize(const char *,const char *,bool,float)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(625,53):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\imgui\imgui.h(859,29):
      see declaration of 'ImGui::CalcTextSize'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(625,40):
      while trying to match the argument list '(std::string)'
  
C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(681,15): error C2664: 'long strtol(const char *,char **,int)': cannot convert argument 1 from 'std::string' to 'const char *'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(681,22):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h(528,23):
      see declaration of 'strtol'
      C:\Users\<USER>\Desktop\coding\ImGui-Loader-Base-master\ui\ui.cc(681,15):
      while trying to match the argument list '(std::string, int, int)'
  
