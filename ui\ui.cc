#include "ui.hh"
#include <Windows.h>
#include "../auth/auth.hpp"
#include <string>
#include <thread>
#include <chrono>
#include <cmath>
#include "../auth/utils.hpp"
#include "../auth/skStr.h"
#include <iostream>
#include <filesystem>

#include "../globals.hh"
#include "../imgui/imgui.h"
#include "../imgui/imgui_internal.h"

using namespace KeyAuth;

// Easing functions for smooth animations
float easeInOutCubic(float t) {
    return t < 0.5f ? 4.0f * t * t * t : 1.0f - pow(-2.0f * t + 2.0f, 3.0f) / 2.0f;
}

float easeInOutSine(float t) {
    return -(cos(3.14159f * t) - 1.0f) / 2.0f;
}

// KeyAuth configuration
std::string name = skCrypt("Nebula").decrypt();
std::string ownerid = skCrypt("uRI84Vg5PZ").decrypt();
std::string version = skCrypt("1.0").decrypt();
std::string url = skCrypt("https://keyauth.win/api/1.3/").decrypt();
std::string path = skCrypt("").decrypt();

api KeyAuthApp(name, ownerid, version, url, path);

// Authentication state variables
static bool keyauth_initialized = false;
static bool show_register = false;
static char license_buf[128] = "";
static char tfa_code_buf[16] = "";
static bool requires_2fa = false;
static std::string auth_error_message = "";

// Session management
std::string tm_to_readable_time(tm ctx);
static std::time_t string_to_timet(std::string timestamp);
static std::tm timet_to_tm(time_t timestamp);
void sessionStatus();

// Initialize KeyAuth
void initializeKeyAuth() {
    if (!keyauth_initialized) {
        KeyAuthApp.init();
        keyauth_initialized = true;

        if (!KeyAuthApp.response.success) {
            auth_error_message = skCrypt("Failed to connect to KeyAuth: ").decrypt() + KeyAuthApp.response.message;
            globals.login_in_progress = false;
            return;
        }

        // Check for auto-login file
        if (std::filesystem::exists(skCrypt("auth.json").decrypt())) {
            if (!CheckIfJsonKeyExists(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt())) {
                // License-based auto-login
                std::string saved_license = ReadFromJson(skCrypt("auth.json").decrypt(), skCrypt("license").decrypt());
                if (!saved_license.empty() && saved_license != skCrypt("File Not Found").decrypt()) {
                    strcpy_s(license_buf, saved_license.c_str());
                    KeyAuthApp.license(saved_license);

                    if (KeyAuthApp.response.success) {
                        globals.logged_in = true;
                        globals.login_in_progress = false;
                        strcpy_s(globals.user_name, KeyAuthApp.user_data.username.c_str());

                        // Start session monitoring
                        std::thread(sessionStatus).detach();
                    }
                }
            } else {
                // Username/password auto-login
                std::string saved_username = ReadFromJson(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt());
                std::string saved_password = ReadFromJson(skCrypt("auth.json").decrypt(), skCrypt("password").decrypt());

                if (!saved_username.empty() && saved_username != skCrypt("File Not Found").decrypt() &&
                    !saved_password.empty() && saved_password != skCrypt("File Not Found").decrypt()) {

                    KeyAuthApp.login(saved_username, saved_password);

                    if (KeyAuthApp.response.success) {
                        globals.logged_in = true;
                        globals.login_in_progress = false;
                        strcpy_s(globals.user_name, KeyAuthApp.user_data.username.c_str());

                        // Start session monitoring
                        std::thread(sessionStatus).detach();
                    }
                }
            }
        }
    }
}

void ui::render() {
    if (!globals.active) return;

    // Initialize KeyAuth on first render
    initializeKeyAuth();

    // Static buffers for input fields
    static char username_buf[64] = "";
    static char password_buf[64] = "";
    static bool show_password = false;

    // Update loading timer and handle authentication
    if (globals.login_in_progress) {
        globals.loading_timer += ImGui::GetIO().DeltaTime;
        // Remove the fake 5-second timer - real auth happens immediately
    }

    // Dynamic window size based on login state and current mode
    ImVec2 window_size_vec;
    if (globals.logged_in) {
        window_size_vec = ImVec2(450, 400);
    } else if (show_register) {
        window_size_vec = ImVec2(350, 350);
    } else if (requires_2fa) {
        window_size_vec = ImVec2(350, 250);
    } else {
        window_size_vec = ImVec2(350, 280);
    }
    ImGui::SetNextWindowSize(window_size_vec, ImGuiCond_Always);

    // Dark theme styling
    ImGui::StyleColorsDark();
    
    // Custom color overrides
    ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.06f, 0.06f, 0.06f, 0.94f));
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.15f, 0.15f, 0.15f, 1.0f));
    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.2f, 0.2f, 1.0f));
    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.3f, 0.3f, 0.3f, 1.0f));
    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.15f, 0.15f, 0.15f, 1.0f));
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.4f, 0.4f, 0.4f, 0.5f));
    ImGui::PushStyleColor(ImGuiCol_Header, ImVec4(0.2f, 0.2f, 0.2f, 1.0f));
    ImGui::PushStyleColor(ImGuiCol_HeaderHovered, ImVec4(0.3f, 0.3f, 0.3f, 1.0f));
    ImGui::PushStyleColor(ImGuiCol_HeaderActive, ImVec4(0.15f, 0.15f, 0.15f, 1.0f));

    // Style variables for rounded corners
    ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, 3.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 3.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 3.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_GrabRounding, 3.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_TabRounding, 3.0f);

    // Window flags for static, clean panel (ohne NoMove!)
    ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoCollapse |
                                   ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoSavedSettings;

    // Set window position (only once, then movable like in your original code)
    ImGui::SetNextWindowPos(ImVec2(window_pos.x, window_pos.y), ImGuiCond_Once);
    ImGui::SetNextWindowBgAlpha(1.0f);

    ImGui::Begin(skCrypt("Login").decrypt().c_str(), &globals.active, window_flags);
    {
        // Rainbow gradient line at the top edge
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        ImVec2 window_pos = ImGui::GetWindowPos();
        ImVec2 window_size = ImGui::GetWindowSize();
        
        // Draw rainbow gradient line at top
        float gradient_height = 3.0f;
        int segments = 50;
        for (int i = 0; i < segments; i++) {
            float t = (float)i / (float)segments;
            float hue = t * 360.0f;
            
            // Convert HSV to RGB for rainbow effect
            float r, g, b;
            if (hue < 60.0f) {
                r = 1.0f; g = hue / 60.0f; b = 0.0f;
            } else if (hue < 120.0f) {
                r = (120.0f - hue) / 60.0f; g = 1.0f; b = 0.0f;
            } else if (hue < 180.0f) {
                r = 0.0f; g = 1.0f; b = (hue - 120.0f) / 60.0f;
            } else if (hue < 240.0f) {
                r = 0.0f; g = (240.0f - hue) / 60.0f; b = 1.0f;
            } else if (hue < 300.0f) {
                r = (hue - 240.0f) / 60.0f; g = 0.0f; b = 1.0f;
            } else {
                r = 1.0f; g = 0.0f; b = (360.0f - hue) / 60.0f;
            }
            
            ImU32 color = IM_COL32((int)(r * 255), (int)(g * 255), (int)(b * 255), 255);
            float x1 = window_pos.x + (window_size.x / segments) * i;
            float x2 = window_pos.x + (window_size.x / segments) * (i + 1);
            
            draw_list->AddRectFilled(
                ImVec2(x1, window_pos.y), 
                ImVec2(x2, window_pos.y + gradient_height), 
                color
            );
        }
        
        ImGui::Spacing();
        ImGui::Spacing();
        
        // Center all content horizontally
        float content_width = ImGui::GetContentRegionAvail().x;
        float item_width = 300.0f;

        if (globals.logged_in) {
            // LOGGED IN STATE - Show tabs and content
            ImGui::Spacing();

            // Success message
            std::string success_msg = "Logged in as " + std::string(globals.user_name) + "!";
            float msg_width = ImGui::CalcTextSize(success_msg.c_str()).x;
            ImGui::SetCursorPosX((content_width - msg_width) * 0.5f);
            ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "%s", success_msg.c_str());

            ImGui::Spacing();

            // Tab Bar
            if (ImGui::BeginTabBar("MainTabs", ImGuiTabBarFlags_None)) {
                if (ImGui::BeginTabItem(skCrypt("Profile").decrypt().c_str())) {
                    ImGui::Spacing();
                    ImGui::Text("%s", skCrypt("User Information").decrypt().c_str());
                    ImGui::Separator();
                    ImGui::Spacing();

                    // Display user data from KeyAuth
                    ImGui::Text("%s %s", skCrypt("Username:").decrypt().c_str(), KeyAuthApp.user_data.username.c_str());
                    ImGui::Text("%s %s", skCrypt("IP Address:").decrypt().c_str(), KeyAuthApp.user_data.ip.c_str());
                    ImGui::Text("%s %s", skCrypt("Hardware ID:").decrypt().c_str(), KeyAuthApp.user_data.hwid.c_str());

                    if (!KeyAuthApp.user_data.createdate.empty()) {
                        ImGui::Text("%s %s", skCrypt("Account Created:").decrypt().c_str(),
                            tm_to_readable_time(timet_to_tm(string_to_timet(KeyAuthApp.user_data.createdate))).c_str());
                    }

                    if (!KeyAuthApp.user_data.lastlogin.empty()) {
                        ImGui::Text("%s %s", skCrypt("Last Login:").decrypt().c_str(),
                            tm_to_readable_time(timet_to_tm(string_to_timet(KeyAuthApp.user_data.lastlogin))).c_str());
                    }

                    ImGui::Spacing();
                    ImGui::Text("%s", skCrypt("Subscriptions:").decrypt().c_str());
                    ImGui::Separator();

                    if (KeyAuthApp.user_data.subscriptions.empty()) {
                        ImGui::Text("%s", skCrypt("No active subscriptions").decrypt().c_str());
                    } else {
                        for (size_t i = 0; i < KeyAuthApp.user_data.subscriptions.size(); i++) {
                            auto sub = KeyAuthApp.user_data.subscriptions.at(i);
                            ImGui::Text("%s %s - %s %s", skCrypt("•").decrypt().c_str(),
                                sub.name.c_str(), skCrypt("Expires:").decrypt().c_str(),
                                tm_to_readable_time(timet_to_tm(string_to_timet(sub.expiry))).c_str());
                        }
                    }

                    ImGui::EndTabItem();
                }

                if (ImGui::BeginTabItem(skCrypt("Settings").decrypt().c_str())) {
                    ImGui::Spacing();
                    ImGui::Text("%s", skCrypt("Settings Configuration").decrypt().c_str());
                    ImGui::Separator();

                    static bool enable_feature1 = true;
                    static bool enable_feature2 = false;
                    static float slider_value = 0.5f;

                    ImGui::Checkbox(skCrypt("Enable Feature 1").decrypt().c_str(), &enable_feature1);
                    ImGui::Checkbox(skCrypt("Enable Feature 2").decrypt().c_str(), &enable_feature2);
                    ImGui::SliderFloat(skCrypt("Value").decrypt().c_str(), &slider_value, 0.0f, 1.0f);

                    ImGui::EndTabItem();
                }

                if (ImGui::BeginTabItem(skCrypt("About").decrypt().c_str())) {
                    ImGui::Spacing();
                    ImGui::Text("%s", skCrypt("About This Application").decrypt().c_str());
                    ImGui::Separator();
                    ImGui::Text("%s", skCrypt("Version: 1.0").decrypt().c_str());
                    ImGui::Text("%s", skCrypt("Author: Your Name").decrypt().c_str());
                    ImGui::Spacing();
                    ImGui::TextWrapped("%s", skCrypt("This is a sample ImGui application with login functionality and tabs.").decrypt().c_str());
                    ImGui::EndTabItem();
                }

                ImGui::EndTabBar();
            }

            ImGui::Spacing();
            ImGui::Separator();
            ImGui::Spacing();

            // Logout Button (full width)
            ImGui::SetCursorPosX((content_width - item_width) * 0.5f);
            if (ImGui::Button(skCrypt("Logout").decrypt().c_str(), ImVec2(item_width, 0))) {
                // Logout from KeyAuth
                KeyAuthApp.logout();

                // Reset to login form
                globals.logged_in = false;
                globals.login_attempted = false;
                globals.login_successful = false;
                globals.login_message = "";
                show_register = false;
                requires_2fa = false;
                auth_error_message = "";

                // Clear input buffers
                memset(username_buf, 0, sizeof(username_buf));
                memset(password_buf, 0, sizeof(password_buf));
                memset(license_buf, 0, sizeof(license_buf));
                memset(tfa_code_buf, 0, sizeof(tfa_code_buf));
                show_password = false;

                // Remove saved credentials
                if (std::filesystem::exists(skCrypt("auth.json").decrypt())) {
                    std::remove(skCrypt("auth.json").decrypt().c_str());
                }
            }

        } else if (globals.login_in_progress) {
            // LOADING STATE - Show loading animation
            ImGui::Spacing();
            ImGui::Spacing();
            ImGui::Spacing();

            // Center the loading content
            float loading_content_height = 100.0f;
            float available_height = ImGui::GetContentRegionAvail().y;
            ImGui::SetCursorPosY(ImGui::GetCursorPosY() + (available_height - loading_content_height) * 0.3f);

            // Spinning circle animation
            ImVec2 center = ImVec2(ImGui::GetCursorScreenPos().x + content_width * 0.5f,
                                  ImGui::GetCursorScreenPos().y + 30);
            float radius = 20.0f;
            float thickness = 3.0f;

            // Update rotation
            globals.loading_rotation += ImGui::GetIO().DeltaTime * 3.0f; // Slower rotation

            ImDrawList* draw_list = ImGui::GetWindowDrawList();

            // Draw spinning arc
            int segments = 30;
            float arc_length = 4.5f; // 3/4 of a circle
            for (int i = 0; i < segments; ++i) {
                float angle1 = globals.loading_rotation + (i * arc_length / segments);
                float angle2 = globals.loading_rotation + ((i + 1) * arc_length / segments);

                float alpha = 1.0f - (float(i) / segments) * 0.7f; // Fade effect
                ImU32 color = ImGui::ColorConvertFloat4ToU32(ImVec4(0.4f, 0.7f, 1.0f, alpha));

                ImVec2 p1 = ImVec2(center.x + cosf(angle1) * radius, center.y + sinf(angle1) * radius);
                ImVec2 p2 = ImVec2(center.x + cosf(angle2) * radius, center.y + sinf(angle2) * radius);

                draw_list->AddLine(p1, p2, color, thickness);
            }

            // Move cursor past the loading circle
            ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 60);

            // Loading text
            std::string loading_text = skCrypt("Logging in...").decrypt();
            float text_width = ImGui::CalcTextSize(loading_text.c_str()).x;
            ImGui::SetCursorPosX((content_width - text_width) * 0.5f);
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "%s", loading_text.c_str());

        } else if (requires_2fa) {
            // 2FA CODE INPUT STATE
            ImGui::Spacing();
            ImGui::Text("%s", skCrypt("Enter 2FA Code:").decrypt().c_str());
            ImGui::Spacing();

            ImGui::SetCursorPosX((content_width - item_width) * 0.5f);
            ImGui::PushItemWidth(item_width);
            std::string tfa_id = skCrypt("##2fa").decrypt();
            std::string tfa_hint = skCrypt("6-digit code").decrypt();
            ImGui::InputTextWithHint(tfa_id.c_str(), tfa_hint.c_str(), tfa_code_buf, sizeof(tfa_code_buf), ImGuiInputTextFlags_CharsDecimal);
            ImGui::PopItemWidth();

            ImGui::Spacing();
            ImGui::Spacing();

            // Submit 2FA Button
            ImGui::SetCursorPosX((content_width - item_width) * 0.5f);
            if (ImGui::Button(skCrypt("Submit").decrypt().c_str(), ImVec2(item_width, 0))) {
                if (strlen(tfa_code_buf) == 0) {
                    auth_error_message = skCrypt("Please enter 2FA code").decrypt();
                } else {
                    globals.login_in_progress = true;

                    // Re-attempt login with 2FA code
                    KeyAuthApp.login(std::string(username_buf), std::string(password_buf), std::string(tfa_code_buf));

                    if (KeyAuthApp.response.success) {
                        globals.logged_in = true;
                        globals.login_in_progress = false;
                        requires_2fa = false;
                        strcpy_s(globals.user_name, KeyAuthApp.user_data.username.c_str());

                        // Save credentials for auto-login
                        WriteToJson(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt(), std::string(username_buf), true, skCrypt("password").decrypt(), std::string(password_buf));

                        // Start session monitoring
                        std::thread(sessionStatus).detach();
                    } else {
                        globals.login_in_progress = false;
                        auth_error_message = KeyAuthApp.response.message;
                    }
                }
            }

            ImGui::Spacing();

            // Back Button
            ImGui::SetCursorPosX((content_width - item_width) * 0.5f);
            if (ImGui::Button(skCrypt("Back").decrypt().c_str(), ImVec2(item_width, 0))) {
                requires_2fa = false;
                auth_error_message = "";
            }

        } else if (show_register) {
            // REGISTER FORM STATE
            ImGui::Spacing();
            ImGui::Text("%s", skCrypt("Create Account").decrypt().c_str());
            ImGui::Separator();
            ImGui::Spacing();

            // Username Field
            ImGui::SetCursorPosX((content_width - item_width) * 0.5f);
            ImGui::PushItemWidth(item_width);
            std::string reg_username_id = skCrypt("##reg_username").decrypt();
            std::string username_hint = skCrypt("Username").decrypt();
            ImGui::InputTextWithHint(reg_username_id.c_str(), username_hint.c_str(), username_buf, sizeof(username_buf));
            ImGui::PopItemWidth();
            ImGui::Spacing();

            // Password Field
            ImGui::SetCursorPosX((content_width - item_width) * 0.5f);
            ImGui::PushItemWidth(item_width - 80.0f);
            ImGuiInputTextFlags password_flags = show_password ? 0 : ImGuiInputTextFlags_Password;
            std::string reg_password_id = skCrypt("##reg_password").decrypt();
            std::string password_hint = skCrypt("Password").decrypt();
            ImGui::InputTextWithHint(reg_password_id.c_str(), password_hint.c_str(), password_buf, sizeof(password_buf), password_flags);
            ImGui::PopItemWidth();
            ImGui::SameLine();

            std::string button_text = show_password ? skCrypt("Hide").decrypt() : skCrypt("Show").decrypt();
            if (ImGui::Button(button_text.c_str(), ImVec2(70, 0))) {
                show_password = !show_password;
            }
            ImGui::Spacing();

            // License Key Field
            ImGui::SetCursorPosX((content_width - item_width) * 0.5f);
            ImGui::PushItemWidth(item_width);
            std::string license_id = skCrypt("##license").decrypt();
            std::string license_hint = skCrypt("License Key").decrypt();
            ImGui::InputTextWithHint(license_id.c_str(), license_hint.c_str(), license_buf, sizeof(license_buf));
            ImGui::PopItemWidth();
            ImGui::Spacing();
            ImGui::Spacing();

            // Register Button
            ImGui::SetCursorPosX((content_width - item_width) * 0.5f);
            if (ImGui::Button(skCrypt("Register").decrypt().c_str(), ImVec2(item_width, 0))) {
                if (strlen(username_buf) == 0 || strlen(password_buf) == 0 || strlen(license_buf) == 0) {
                    auth_error_message = skCrypt("Please fill all fields").decrypt();
                } else {
                    globals.login_in_progress = true;
                    auth_error_message = "";

                    // Perform KeyAuth registration
                    KeyAuthApp.regstr(std::string(username_buf), std::string(password_buf), std::string(license_buf));

                    if (KeyAuthApp.response.success) {
                        globals.logged_in = true;
                        globals.login_in_progress = false;
                        show_register = false;
                        strcpy_s(globals.user_name, KeyAuthApp.user_data.username.c_str());

                        // Save credentials for auto-login
                        WriteToJson(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt(), std::string(username_buf), true, skCrypt("password").decrypt(), std::string(password_buf));

                        // Start session monitoring
                        std::thread(sessionStatus).detach();
                    } else {
                        globals.login_in_progress = false;
                        auth_error_message = KeyAuthApp.response.message;
                    }
                }
            }

            ImGui::Spacing();

            // Back to Login Button
            ImGui::SetCursorPosX((content_width - item_width) * 0.5f);
            if (ImGui::Button(skCrypt("Back to Login").decrypt().c_str(), ImVec2(item_width, 0))) {
                show_register = false;
                auth_error_message = "";
            }

        } else {
            // LOGIN FORM STATE - Show username, password, and buttons

            // Username Field
            ImGui::SetCursorPosX((content_width - item_width) * 0.5f);
            ImGui::PushItemWidth(item_width);
            std::string username_id = skCrypt("##username").decrypt();
            std::string username_hint2 = skCrypt("Username").decrypt();
            ImGui::InputTextWithHint(username_id.c_str(), username_hint2.c_str(), username_buf, sizeof(username_buf));
            ImGui::PopItemWidth();
            ImGui::Spacing();

            // Password Field with Show/Hide functionality
            ImGui::SetCursorPosX((content_width - item_width) * 0.5f);
            ImGui::PushItemWidth(item_width - 80.0f); // Leave space for button
            ImGuiInputTextFlags password_flags = show_password ? 0 : ImGuiInputTextFlags_Password;
            std::string password_id = skCrypt("##password").decrypt();
            std::string password_hint2 = skCrypt("Password").decrypt();
            ImGui::InputTextWithHint(password_id.c_str(), password_hint2.c_str(), password_buf, sizeof(password_buf), password_flags);
            ImGui::PopItemWidth();
            ImGui::SameLine();

            // Show/Hide Button
            std::string button_text = show_password ? skCrypt("Hide").decrypt() : skCrypt("Show").decrypt();
            if (ImGui::Button(button_text.c_str(), ImVec2(70, 0))) {
                show_password = !show_password;
            }

            ImGui::Spacing();
            ImGui::Spacing();

            // Login Button (full width)
            ImGui::SetCursorPosX((content_width - item_width) * 0.5f);
            if (ImGui::Button(skCrypt("Login").decrypt().c_str(), ImVec2(item_width, 0))) {
                if (strlen(username_buf) == 0 || strlen(password_buf) == 0) {
                    auth_error_message = skCrypt("Please enter both username and password").decrypt();
                    return;
                }

                globals.login_in_progress = true;
                globals.loading_timer = 0.0f;
                globals.loading_rotation = 0.0f;
                auth_error_message = "";
                requires_2fa = false;

                // Perform KeyAuth login
                KeyAuthApp.login(std::string(username_buf), std::string(password_buf));

                if (KeyAuthApp.response.success) {
                    globals.logged_in = true;
                    globals.login_in_progress = false;
                    strcpy_s(globals.user_name, KeyAuthApp.user_data.username.c_str());

                    // Save credentials for auto-login
                    WriteToJson(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt(), std::string(username_buf), true, skCrypt("password").decrypt(), std::string(password_buf));

                    // Start session monitoring
                    std::thread(sessionStatus).detach();
                } else {
                    globals.login_in_progress = false;
                    if (KeyAuthApp.response.message == skCrypt("2FA code required.").decrypt()) {
                        requires_2fa = true;
                        auth_error_message = skCrypt("2FA code required").decrypt();
                    } else {
                        auth_error_message = KeyAuthApp.response.message;
                    }
                }
            }

            ImGui::Spacing();

            // Exit Button (full width)
            ImGui::SetCursorPosX((content_width - item_width) * 0.5f);
            if (ImGui::Button(skCrypt("Exit").decrypt().c_str(), ImVec2(item_width, 0))) {
                globals.active = false; // Close the application
            }

            ImGui::Spacing();

            // Sign Up Link (centered) - improvement #2
            std::string signup_text1 = skCrypt("Don't have an account? ").decrypt();
            std::string signup_text2 = skCrypt("Sign Up").decrypt();

            float text1_width = ImGui::CalcTextSize(signup_text1.c_str()).x;
            float text2_width = ImGui::CalcTextSize(signup_text2.c_str()).x;
            float total_width = text1_width + text2_width;

            ImGui::SetCursorPosX((content_width - total_width) * 0.5f);

            // Regular text
            ImGui::Text("%s", signup_text1.c_str());
            ImGui::SameLine(0, 0); // No spacing between texts

            // Clickable "Sign Up" text in light blue with hand cursor
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.4f, 0.7f, 1.0f, 1.0f)); // Light blue

            // Use Selectable instead of Text for better click detection
            if (ImGui::Selectable(signup_text2.c_str(), false, ImGuiSelectableFlags_DontClosePopups, ImVec2(text2_width, 0))) {
                show_register = true;
                auth_error_message = "";
            }

            // Check if hovering over the sign up text
            if (ImGui::IsItemHovered()) {
                ImGui::SetMouseCursor(ImGuiMouseCursor_Hand);
            }

            ImGui::PopStyleColor();
        }

        // Show error messages if any
        if (!auth_error_message.empty()) {
            ImGui::Spacing();
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.3f, 0.3f, 1.0f)); // Red color

            // Center the error message
            float error_width = ImGui::CalcTextSize(auth_error_message.c_str()).x;
            ImGui::SetCursorPosX((content_width - error_width) * 0.5f);
            ImGui::TextWrapped("%s", auth_error_message.c_str());

            ImGui::PopStyleColor();
        }
    }
    ImGui::End();
    
    // Pop all style colors and variables
    ImGui::PopStyleColor(9); // 9 color pushes
    ImGui::PopStyleVar(5);   // 5 style var pushes
}

void ui::init(LPDIRECT3DDEVICE9 device) {
    dev = device;

    // Set up window position if not set
    if (window_pos.x == 0) {
        RECT screen_rect{};
        GetWindowRect(GetDesktopWindow(), &screen_rect);
        screen_res = ImVec2(float(screen_rect.right), float(screen_rect.bottom));
        window_pos = (screen_res - window_size) * 0.5f;
    }
}

// Session status monitoring function
void sessionStatus() {
    KeyAuthApp.check(true); // Initial check
    if (!KeyAuthApp.response.success) {
        globals.logged_in = false;
        globals.active = false;
        return;
    }

    if (KeyAuthApp.response.isPaid) {
        while (globals.logged_in && globals.active) {
            Sleep(20000); // Check every 20 seconds
            KeyAuthApp.check();
            if (!KeyAuthApp.response.success) {
                globals.logged_in = false;
                globals.active = false;
                break;
            }
        }
    }
}

// Helper functions for time conversion
std::string tm_to_readable_time(tm ctx) {
    char buffer[80];
    strftime(buffer, sizeof(buffer), "%a %m/%d/%y %H:%M:%S %Z", &ctx);
    return std::string(buffer);
}

static std::time_t string_to_timet(std::string timestamp) {
    auto cv = strtol(timestamp.c_str(), NULL, 10);
    return (time_t)cv;
}

static std::tm timet_to_tm(time_t timestamp) {
    std::tm context;
    localtime_s(&context, &timestamp);
    return context;
}
